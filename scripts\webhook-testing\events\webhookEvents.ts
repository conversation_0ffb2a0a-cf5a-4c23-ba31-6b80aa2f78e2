/**
 * WEBHOOK EVENT DEFINITIONS
 * 
 * Defines all Razorpay webhook events and their payload structures
 * for comprehensive testing.
 */

export interface WebhookEventDefinition {
  eventType: string;
  description: string;
  category: 'subscription' | 'payment' | 'invoice' | 'refund';
  triggerConditions: string[];
  expectedOutcomes: string[];
}

/**
 * SUBSCRIPTION WEBHOOK EVENTS
 */
export const SUBSCRIPTION_EVENTS: WebhookEventDefinition[] = [
  {
    eventType: 'subscription.authenticated',
    description: 'Triggered when customer authenticates a subscription (selects plan)',
    category: 'subscription',
    triggerConditions: [
      'User selects a plan during trial',
      'User changes plan selection',
      'Plan upgrade/downgrade initiated'
    ],
    expectedOutcomes: [
      'Subscription status changes to authenticated',
      'Plan details are updated',
      'has_active_subscription remains false until payment'
    ]
  },
  {
    eventType: 'subscription.activated',
    description: 'Triggered when subscription becomes active (payment successful)',
    category: 'subscription',
    triggerConditions: [
      'First payment successful after authentication',
      'Halted subscription reactivated',
      'Direct activation without authentication'
    ],
    expectedOutcomes: [
      'Subscription status changes to active',
      'has_active_subscription becomes true',
      'Subscription dates are updated'
    ]
  },
  {
    eventType: 'subscription.charged',
    description: 'Triggered when subscription payment is successfully charged',
    category: 'subscription',
    triggerConditions: [
      'Recurring payment successful',
      'Manual charge successful',
      'Retry payment successful'
    ],
    expectedOutcomes: [
      'Payment details updated',
      'Subscription dates refreshed',
      'Next billing cycle calculated'
    ]
  },
  {
    eventType: 'subscription.pending',
    description: 'Triggered when subscription is in pending state',
    category: 'subscription',
    triggerConditions: [
      'Payment authorization pending',
      'Manual review required',
      'Bank processing delay'
    ],
    expectedOutcomes: [
      'Subscription status changes to pending',
      'has_active_subscription remains false',
      'Awaiting further action'
    ]
  },
  {
    eventType: 'subscription.halted',
    description: 'Triggered when subscription is paused/halted',
    category: 'subscription',
    triggerConditions: [
      'Payment failure',
      'Insufficient funds',
      'Card expired',
      'Manual halt by merchant'
    ],
    expectedOutcomes: [
      'Subscription status changes to halted',
      'has_active_subscription becomes false',
      'Business profile status may change to offline'
    ]
  },
  {
    eventType: 'subscription.cancelled',
    description: 'Triggered when subscription is cancelled',
    category: 'subscription',
    triggerConditions: [
      'User cancels active subscription',
      'User cancels authenticated subscription (Plan A)',
      'Merchant cancels subscription',
      'System cancellation due to policy'
    ],
    expectedOutcomes: [
      'Active subscription: downgrade to free plan',
      'Authenticated subscription: revert to trial',
      'has_active_subscription becomes false',
      'Cancellation timestamp recorded'
    ]
  },
  {
    eventType: 'subscription.completed',
    description: 'Triggered when subscription completes all billing cycles',
    category: 'subscription',
    triggerConditions: [
      'Fixed-term subscription ends',
      'All planned charges completed',
      'Subscription reaches end date'
    ],
    expectedOutcomes: [
      'Subscription downgrades to free plan',
      'has_active_subscription becomes false',
      'Completion timestamp recorded'
    ]
  },
  {
    eventType: 'subscription.expired',
    description: 'Triggered when subscription expires',
    category: 'subscription',
    triggerConditions: [
      'Trial period expires',
      'Grace period expires after payment failure',
      'Subscription reaches expiry date'
    ],
    expectedOutcomes: [
      'Subscription downgrades to free plan',
      'has_active_subscription becomes false',
      'Expiry timestamp recorded'
    ]
  },
  {
    eventType: 'subscription.updated',
    description: 'Triggered when subscription details are updated',
    category: 'subscription',
    triggerConditions: [
      'Plan change',
      'Billing cycle change',
      'Customer details update',
      'Subscription metadata update'
    ],
    expectedOutcomes: [
      'Subscription details updated',
      'Plan limits may change',
      'Billing dates may be recalculated'
    ]
  }
];

/**
 * PAYMENT WEBHOOK EVENTS
 */
export const PAYMENT_EVENTS: WebhookEventDefinition[] = [
  {
    eventType: 'payment.authorized',
    description: 'Triggered when payment is authorized but not captured',
    category: 'payment',
    triggerConditions: [
      'Card authorization successful',
      'Bank approval received',
      'Two-factor authentication completed'
    ],
    expectedOutcomes: [
      'Payment status changes to authorized',
      'Funds are held but not transferred',
      'Awaiting capture or auto-capture'
    ]
  },
  {
    eventType: 'payment.captured',
    description: 'Triggered when authorized payment is captured',
    category: 'payment',
    triggerConditions: [
      'Manual capture by merchant',
      'Auto-capture after authorization',
      'Partial capture of authorized amount'
    ],
    expectedOutcomes: [
      'Payment status changes to captured',
      'Funds are transferred to merchant',
      'Payment method details updated'
    ]
  },
  {
    eventType: 'payment.failed',
    description: 'Triggered when payment fails',
    category: 'payment',
    triggerConditions: [
      'Insufficient funds',
      'Card declined',
      'Network error',
      'Bank rejection'
    ],
    expectedOutcomes: [
      'Payment status changes to failed',
      'Subscription may be halted',
      'Retry mechanism may be triggered'
    ]
  }
];

/**
 * INVOICE WEBHOOK EVENTS
 */
export const INVOICE_EVENTS: WebhookEventDefinition[] = [
  {
    eventType: 'invoice.paid',
    description: 'Triggered when invoice is successfully paid',
    category: 'invoice',
    triggerConditions: [
      'Subscription invoice paid',
      'One-time invoice paid',
      'Partial payment received'
    ],
    expectedOutcomes: [
      'Invoice status changes to paid',
      'Payment method details recorded',
      'Subscription may be activated/renewed'
    ]
  }
];

/**
 * REFUND WEBHOOK EVENTS
 */
export const REFUND_EVENTS: WebhookEventDefinition[] = [
  {
    eventType: 'refund.created',
    description: 'Triggered when refund is initiated',
    category: 'refund',
    triggerConditions: [
      'Merchant initiates refund',
      'Customer requests refund',
      'Automatic refund due to policy'
    ],
    expectedOutcomes: [
      'Refund record created',
      'Refund status set to created',
      'Processing begins'
    ]
  },
  {
    eventType: 'refund.processed',
    description: 'Triggered when refund is successfully processed',
    category: 'refund',
    triggerConditions: [
      'Bank processes refund',
      'Funds returned to customer',
      'Refund completion confirmed'
    ],
    expectedOutcomes: [
      'Refund status changes to processed',
      'Customer receives funds',
      'Subscription may be cancelled'
    ]
  },
  {
    eventType: 'refund.failed',
    description: 'Triggered when refund fails',
    category: 'refund',
    triggerConditions: [
      'Bank rejects refund',
      'Invalid account details',
      'Technical error'
    ],
    expectedOutcomes: [
      'Refund status changes to failed',
      'Manual intervention required',
      'Customer notification sent'
    ]
  }
];

/**
 * ALL WEBHOOK EVENTS COMBINED
 */
export const ALL_WEBHOOK_EVENTS = [
  ...SUBSCRIPTION_EVENTS,
  ...PAYMENT_EVENTS,
  ...INVOICE_EVENTS,
  ...REFUND_EVENTS
];

/**
 * Get events by category
 */
export function getEventsByCategory(category: WebhookEventDefinition['category']): WebhookEventDefinition[] {
  return ALL_WEBHOOK_EVENTS.filter(event => event.category === category);
}

/**
 * Get event by type
 */
export function getEventByType(eventType: string): WebhookEventDefinition | undefined {
  return ALL_WEBHOOK_EVENTS.find(event => event.eventType === eventType);
}

/**
 * WEBHOOK PAYLOAD GENERATORS
 */
export function generateWebhookPayload(
  eventType: string,
  subscriptionId: string,
  businessProfileId: string,
  additionalData: Record<string, unknown> = {}
): unknown {
  // Handle charged events differently as they contain payment data
  if (eventType === 'subscription.charged') {
    return {
      entity: 'event',
      account_id: 'acc_test',
      event: eventType,
      contains: ['payment'],
      created_at: Math.floor(Date.now() / 1000),
      payload: {
        payment: {
          entity: {
            id: `pay_test_${Date.now()}`,
            amount: 99900, // ₹999
            currency: 'INR',
            status: 'captured',
            method: additionalData.payment_method || 'card',
            captured: true,
            subscription_id: subscriptionId,
            notes: {
              business_profile_id: businessProfileId,
              plan_type: additionalData.plan_id || 'growth',
              plan_cycle: additionalData.plan_cycle || 'monthly',
              ...(additionalData.old_subscription_id ? { old_subscription_id: additionalData.old_subscription_id } : {})
            },
            created_at: Math.floor(Date.now() / 1000)
          }
        }
      }
    };
  }

  // Handle halted events with proper subscription structure
  if (eventType === 'subscription.halted') {
    return {
      entity: 'event',
      account_id: 'acc_test',
      event: eventType,
      contains: ['subscription'],
      created_at: Math.floor(Date.now() / 1000),
      payload: {
        subscription: {
          entity: {
            id: subscriptionId,
            status: 'halted',
            payment_method: additionalData.payment_method || 'card',
            notes: {
              business_profile_id: businessProfileId,
              plan_type: additionalData.plan_id || 'growth',
              plan_cycle: additionalData.plan_cycle || 'monthly',
              ...(additionalData.payment_method ? { payment_method: additionalData.payment_method } : {})
            },
            current_start: Math.floor(Date.now() / 1000),
            current_end: Math.floor(Date.now() / 1000) + 2592000, // +30 days
            charge_at: Math.floor(Date.now() / 1000) + 2592000,
            customer_id: `cust_test_${Date.now()}`
          }
        }
      }
    };
  }

  // Default subscription payload for other events
  const basePayload = {
    entity: 'event',
    account_id: 'acc_test',
    event: eventType,
    contains: ['subscription'],
    created_at: Math.floor(Date.now() / 1000),
    payload: {
      subscription: {
        entity: {
          id: subscriptionId,
          status: getStatusFromEvent(eventType),
          payment_method: additionalData.payment_method || 'card',
          notes: {
            business_profile_id: businessProfileId,
            plan_type: additionalData.plan_id || 'growth',
            plan_cycle: additionalData.plan_cycle || 'monthly',
            ...(additionalData.payment_method ? { payment_method: additionalData.payment_method } : {}),
            ...(additionalData.old_subscription_id ? { old_subscription_id: additionalData.old_subscription_id } : {}),
            ...(additionalData.last_payment_id ? { last_payment_id: additionalData.last_payment_id } : {})
          },
          current_start: Math.floor(Date.now() / 1000),
          current_end: Math.floor(Date.now() / 1000) + 2592000, // +30 days
          charge_at: Math.floor(Date.now() / 1000) + 2592000,
          customer_id: `cust_test_${Date.now()}`,
          // Remove additionalData spread to avoid conflicts
          ...(additionalData.current_start ? { current_start: additionalData.current_start } : {}),
          ...(additionalData.current_end ? { current_end: additionalData.current_end } : {}),
          ...(additionalData.charge_at ? { charge_at: additionalData.charge_at } : {})
        }
      }
    }
  };

  return basePayload;
}

/**
 * Map webhook event to expected subscription status
 */
function getStatusFromEvent(eventType: string): string {
  const eventToStatusMap: Record<string, string> = {
    'subscription.authenticated': 'authenticated',
    'subscription.activated': 'active',
    'subscription.charged': 'active',
    'subscription.pending': 'pending',
    'subscription.halted': 'halted',
    'subscription.cancelled': 'cancelled',
    'subscription.completed': 'completed',
    'subscription.expired': 'expired',
    'subscription.updated': 'active'
  };

  return eventToStatusMap[eventType] || 'active';
}
