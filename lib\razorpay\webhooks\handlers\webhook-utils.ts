import { SubscriptionStatus, SUBSCRIPTION_STATUS } from "./subscription-constants";

/**
 * ENHANCED WEBHOOK TIMESTAMP EXTRACTION
 *
 * Extracts webhook timestamp from Razorpay payload for sequence validation.
 * This function is critical for preventing out-of-order webhook processing.
 *
 * @param payload The webhook payload from Razorpay
 * @returns Unix timestamp in seconds, or current time if not available
 */
export function extractWebhookTimestamp(payload: any): number { // eslint-disable-line @typescript-eslint/no-explicit-any
  try {
    // Priority order for timestamp extraction (most reliable first)
    const timestampSources = [
      // 1. Main event timestamp (most reliable)
      () => payload.created_at,

      // 2. Subscription entity timestamps
      () => payload.payload?.subscription?.entity?.created_at,
      () => payload.payload?.subscription?.entity?.updated_at,

      // 3. Payment entity timestamps
      () => payload.payload?.payment?.entity?.created_at,
      () => payload.payload?.payment?.entity?.updated_at,

      // 4. Invoice entity timestamps
      () => payload.payload?.invoice?.entity?.created_at,
      () => payload.payload?.invoice?.entity?.updated_at,

      // 5. Generic entity timestamps
      () => payload.payload?.entity?.created_at,
      () => payload.payload?.entity?.updated_at,

      // 6. Event-specific timestamps
      () => payload.event_timestamp,
      () => payload.timestamp
    ];

    // Try each timestamp source in order
    for (const getTimestamp of timestampSources) {
      try {
        const timestamp = getTimestamp();
        if (timestamp && typeof timestamp === 'number' && timestamp > 0) {
          // Validate timestamp is reasonable (not too far in past/future)
          const now = Math.floor(Date.now() / 1000);
          const maxAge = 24 * 60 * 60; // 24 hours
          const maxFuture = 5 * 60; // 5 minutes

          if (timestamp >= (now - maxAge) && timestamp <= (now + maxFuture)) {
            return timestamp;
          } else {
            console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${timestamp} outside reasonable range, trying next source`);
          }
        }
      } catch (_sourceError) {
        // Continue to next source if this one fails
        continue;
      }
    }

    // Fallback to current time with warning
    console.warn('[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time');
    console.warn('[WEBHOOK_TIMESTAMP] Payload structure:', JSON.stringify(payload, null, 2));
    return Math.floor(Date.now() / 1000);

  } catch (error) {
    console.error('[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:', error);
    return Math.floor(Date.now() / 1000);
  }
}

/**
 * Gets the appropriate subscription status for a user based on their current state.
 * This is used to determine the correct status when creating or updating subscriptions.
 *
 * @param hasActiveSubscription Current has_active_subscription flag (should be false for trial/free)
 * @param trialEndDate Trial end date if any
 * @param planId Current plan ID
 * @returns The appropriate subscription status
 */
export function getAppropriateSubscriptionStatus(
  hasActiveSubscription: boolean,
  trialEndDate: string | null,
  planId: string
): SubscriptionStatus {
  // If on free plan, always active status but has_active_subscription should be false
  if (planId === 'free') {
    return SUBSCRIPTION_STATUS.ACTIVE;
  }

  // Check if user is in trial period
  if (trialEndDate) {
    const trialEnd = new Date(trialEndDate);
    const now = new Date();

    if (trialEnd > now) {
      // User is in trial period - status is trial, has_active_subscription should be false
      return SUBSCRIPTION_STATUS.TRIAL;
    }
  }

  // If has active subscription flag, it means they have a paid subscription
  if (hasActiveSubscription) {
    return SUBSCRIPTION_STATUS.ACTIVE;
  }

  // Default to pending if no active subscription and not in trial
  return SUBSCRIPTION_STATUS.PENDING;
}