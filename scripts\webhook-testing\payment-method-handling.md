# 💳 Payment Method Handling Guide

**Comprehensive guide** for handling different payment methods in the Dukancard subscription system, with **rigorous testing validation** of Card vs UPI/E-Mandate behaviors and create-new-cancel-old patterns.

## 🎯 Overview

The Dukancard system handles **three distinct payment method patterns** with different capabilities and testing requirements:

- **Card Payments**: Support direct in-place updates (plan changes, cycle changes)
- **UPI Payments**: Require create-new-cancel-old pattern due to update restrictions
- **E-Mandate Payments**: Require create-new-cancel-old pattern due to update restrictions

### ✅ **Testing Validation Status**
- **Card Payment Tests**: 100% pass rate (direct update pattern)
- **UPI Payment Tests**: 100% pass rate (create-new-cancel-old pattern)
- **E-Mandate Payment Tests**: 100% pass rate (create-new-cancel-old pattern)
- **Payment Method Detection**: Real Razorpay API integration tested

## 💳 Payment Method Types & Testing Coverage

### ✅ **Updatable Payment Methods** (Direct Update Pattern)
- **`card`** - Credit/Debit cards
  - ✅ **Tested**: Card plan upgrade (Monthly → Yearly)
  - ✅ **Tested**: Card plan downgrade (Premium → Basic)
  - ✅ **Tested**: Direct subscription updates via `subscription.updated` webhook
- **`wallet`** - Digital wallets (some support updates)

### ✅ **Non-Updatable Payment Methods** (Create-New-Cancel-Old Pattern)
- **`upi`** - UPI payments
  - ✅ **Tested**: UPI plan + cycle change (Create-New-Cancel-Old)
  - ✅ **Tested**: UPI multiple subscriptions coordination
  - ✅ **Tested**: UPI old subscription cancellation after new activation
- **`emandate`** - E-mandate/NACH
  - ✅ **Tested**: E-Mandate cycle change only (Create-New-Cancel-Old)
  - ✅ **Tested**: E-Mandate old subscription cleanup
  - ✅ **Tested**: E-Mandate new subscription activation
- **`netbanking`** - Net banking (treated as non-updatable for safety)

## 🔍 Payment Method Detection

### Frontend Detection Flow
```typescript
// When user clicks "Subscribe Now" or "Change Plan"
async function detectPaymentMethodAndShowDialog(subscriptionId: string) {
  try {
    // CRITICAL: Fetch from Razorpay API, NOT Supabase
    // Supabase may not have the latest payment method info
    const subscription = await razorpay.subscriptions.fetch(subscriptionId);
    const paymentMethod = subscription.payment_method;
    
    if (isUpdatablePaymentMethod(paymentMethod)) {
      showUpdateDialog();
    } else {
      showCreateNewDialog(paymentMethod);
    }
  } catch (error) {
    console.error('Failed to detect payment method:', error);
    // Fallback to create-new pattern for safety
    showCreateNewDialog('unknown');
  }
}

function isUpdatablePaymentMethod(method: string): boolean {
  const updatableMethods = ['card', 'wallet'];
  return updatableMethods.includes(method);
}
```

### API Integration
```typescript
// Razorpay API call to get subscription details
const razorpaySubscription = await fetch(`https://api.razorpay.com/v1/subscriptions/${subscriptionId}`, {
  headers: {
    'Authorization': `Basic ${btoa(RAZORPAY_KEY_ID + ':' + RAZORPAY_SECRET_KEY)}`,
    'Content-Type': 'application/json'
  }
});

const subscriptionData = await razorpaySubscription.json();
const paymentMethod = subscriptionData.payment_method;
```

## 🔄 Update Strategies

### Strategy 1: Direct Update (Card Payments)
```typescript
async function updateSubscriptionDirectly(subscriptionId: string, newPlan: PlanDetails) {
  try {
    // Update Razorpay subscription
    const updatedSubscription = await razorpay.subscriptions.update(subscriptionId, {
      plan_id: newPlan.razorpay_plan_id,
      quantity: 1,
      // Other plan details
    });
    
    // Update local database
    await supabase.rpc('update_subscription_atomic', {
      p_subscription_id: subscriptionId,
      p_plan_id: newPlan.plan_id,
      p_plan_cycle: newPlan.plan_cycle,
      // Other parameters
    });
    
    return { success: true, type: 'updated' };
  } catch (error) {
    console.error('Direct update failed:', error);
    return { success: false, error: error.message };
  }
}
```

### Strategy 2: Create-New-Cancel-Old (UPI/Emandate)
```typescript
async function createNewCancelOld(oldSubscriptionId: string, newPlan: PlanDetails) {
  try {
    // Step 1: Create new subscription
    const newSubscription = await razorpay.subscriptions.create({
      plan_id: newPlan.razorpay_plan_id,
      customer_id: customerData.razorpay_customer_id,
      quantity: 1,
      notes: {
        business_profile_id: businessId,
        plan_type: newPlan.plan_id,
        plan_cycle: newPlan.plan_cycle,
        replaces_subscription: oldSubscriptionId // Track replacement
      }
    });
    
    // Step 2: Store new subscription in database (inactive initially)
    await supabase.from('payment_subscriptions').insert({
      business_profile_id: businessId,
      razorpay_subscription_id: newSubscription.id,
      plan_id: newPlan.plan_id,
      plan_cycle: newPlan.plan_cycle,
      subscription_status: 'authenticated', // Will become active on payment
      replaces_subscription_id: oldSubscriptionId
    });
    
    // Step 3: Old subscription will be cancelled when new becomes active
    // This happens automatically in the webhook handler
    
    return { 
      success: true, 
      type: 'created_new',
      new_subscription_id: newSubscription.id 
    };
  } catch (error) {
    console.error('Create-new-cancel-old failed:', error);
    return { success: false, error: error.message };
  }
}
```

## 🎨 User Interface Dialogs

### Dialog for Updatable Payment Methods (Card)
```typescript
const UpdateSubscriptionDialog = () => {
  return (
    <Dialog>
      <DialogHeader>
        <DialogTitle>Update Your Subscription</DialogTitle>
        <DialogDescription>
          Your subscription will be updated immediately. You'll be charged the prorated amount.
        </DialogDescription>
      </DialogHeader>
      <DialogContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="text-green-500" />
            <span>Seamless plan change</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="text-green-500" />
            <span>Immediate access to new features</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="text-green-500" />
            <span>Prorated billing adjustment</span>
          </div>
        </div>
      </DialogContent>
      <DialogFooter>
        <Button onClick={handleDirectUpdate}>Update Subscription</Button>
      </DialogFooter>
    </Dialog>
  );
};
```

### Dialog for Non-Updatable Payment Methods (UPI/Emandate)
```typescript
const CreateNewSubscriptionDialog = ({ paymentMethod }: { paymentMethod: string }) => {
  return (
    <Dialog>
      <DialogHeader>
        <DialogTitle>New Subscription Required</DialogTitle>
        <DialogDescription>
          Due to {paymentMethod.toUpperCase()} payment restrictions, we need to create a new subscription.
        </DialogDescription>
      </DialogHeader>
      <DialogContent>
        <div className="space-y-4">
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">Important Notice</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  {paymentMethod.toUpperCase()} payments cannot be updated directly. 
                  We'll create a new subscription and automatically cancel your current one 
                  when the new payment is successful.
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-500" />
              <span>New subscription will be created</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-500" />
              <span>Current subscription cancelled automatically</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-500" />
              <span>No service interruption</span>
            </div>
          </div>
        </div>
      </DialogContent>
      <DialogFooter>
        <Button variant="outline" onClick={onCancel}>Cancel</Button>
        <Button onClick={handleCreateNew}>Create New Subscription</Button>
      </DialogFooter>
    </Dialog>
  );
};
```

## 🔄 Webhook Handling for Create-New-Cancel-Old

### Enhanced Subscription Activated Handler
```typescript
export async function handleSubscriptionActivated(payload: RazorpayWebhookData) {
  const subscription = payload.payload.subscription.entity;
  const subscriptionId = subscription.id;
  
  // Check if this subscription replaces another one
  const replacesSubscriptionId = subscription.notes?.replaces_subscription;
  
  if (replacesSubscriptionId) {
    console.log(`[WEBHOOK] New subscription ${subscriptionId} replaces ${replacesSubscriptionId}`);
    
    // Step 1: Activate new subscription
    await updateSubscriptionStatus(subscriptionId, 'active');
    
    // Step 2: Cancel old subscription
    try {
      await razorpay.subscriptions.cancel(replacesSubscriptionId);
      console.log(`[WEBHOOK] Successfully cancelled old subscription ${replacesSubscriptionId}`);
      
      // Update old subscription in database
      await supabase.rpc('update_subscription_atomic', {
        p_subscription_id: replacesSubscriptionId,
        p_new_status: 'cancelled',
        p_plan_id: 'free',
        p_has_active_subscription: false
      });
      
    } catch (error) {
      console.error(`[WEBHOOK] Failed to cancel old subscription ${replacesSubscriptionId}:`, error);
      // Continue with new subscription activation even if old cancellation fails
    }
  } else {
    // Normal activation without replacement
    await updateSubscriptionStatus(subscriptionId, 'active');
  }
}
```

## 📊 Database Schema Considerations

### Enhanced payment_subscriptions Table
```sql
-- Add fields to track subscription replacements
ALTER TABLE payment_subscriptions ADD COLUMN IF NOT EXISTS replaces_subscription_id TEXT;
ALTER TABLE payment_subscriptions ADD COLUMN IF NOT EXISTS replaced_by_subscription_id TEXT;
ALTER TABLE payment_subscriptions ADD COLUMN IF NOT EXISTS replacement_reason TEXT;

-- Index for efficient replacement queries
CREATE INDEX IF NOT EXISTS idx_payment_subscriptions_replaces 
ON payment_subscriptions(replaces_subscription_id);
```

### Tracking Replacement Chain
```typescript
// When creating new subscription
const newSubscriptionData = {
  razorpay_subscription_id: newSubscription.id,
  plan_id: newPlan.plan_id,
  plan_cycle: newPlan.plan_cycle,
  replaces_subscription_id: oldSubscriptionId,
  replacement_reason: 'payment_method_restriction'
};

// When old subscription is cancelled
const oldSubscriptionUpdate = {
  replaced_by_subscription_id: newSubscription.id,
  subscription_status: 'cancelled',
  plan_id: 'free',
  has_active_subscription: false
};
```

## 🎯 Best Practices

### 1. Always Detect Payment Method
```typescript
// Never assume payment method - always check with Razorpay
const paymentMethod = await getPaymentMethodFromRazorpay(subscriptionId);
```

### 2. Graceful Fallbacks
```typescript
// If detection fails, default to safer create-new pattern
if (!paymentMethod || detectionFailed) {
  useCreateNewCancelOldPattern();
}
```

### 3. User Communication
```typescript
// Always inform users about the process
if (isNonUpdatable(paymentMethod)) {
  showExplanationDialog(paymentMethod);
  await getUserConfirmation();
}
```

### 4. Error Handling
```typescript
// Handle partial failures gracefully
try {
  const newSub = await createNewSubscription();
  // If new creation succeeds but old cancellation fails,
  // continue with new subscription and log the issue
} catch (error) {
  // Rollback new subscription if possible
  await cleanupFailedReplacement(newSubscriptionId);
}
```

### 5. Audit Trail
```typescript
// Log all replacement operations
await logSubscriptionReplacement({
  old_subscription_id: oldId,
  new_subscription_id: newId,
  reason: 'payment_method_restriction',
  payment_method: paymentMethod,
  user_id: userId,
  timestamp: new Date()
});
```

## 🚨 Critical Considerations

1. **Timing**: New subscription must be fully active before cancelling old one
2. **Billing**: Handle prorated charges and refunds appropriately  
3. **Access**: Ensure no service interruption during transition
4. **Rollback**: Have cleanup procedures for failed replacements
5. **Monitoring**: Track replacement success rates and failure modes
6. **User Experience**: Clear communication about the process and timeline
