"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";


import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";


interface UseSubscriptionHandlerProps {
  currentSubscriptionId: string | null;
  subscriptionStatus: SubscriptionStatus;
  currentPlanDetails?: PricingPlan;
  currentPlanCycle: "monthly" | "yearly";
  lastPaymentMethod?: string | null;
  razorpaySubscriptionId?: string | null;

  // From subscription logic hook
  dialogPlan: PricingPlan | null;
  billingCycle: "monthly" | "yearly";
  setDialogLoading: (_loading: boolean) => void;
  setIsPlanDialogOpen: (_open: boolean) => void;
  setIsUpiWarningDialogOpen: (_open: boolean) => void;
  setPendingSubscribeAction: (_action: (() => Promise<Response | undefined>) | null) => void;

  // Processing functions
  startProcessing: (_message: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  resetProcessing: () => void;
  setSubscriptionCreated: (_message: string) => void;
  setFuturePaymentAuthorized: (_message: string) => void;

  // Validation and helper functions
  validateSubscriptionRequest: (_plan: PricingPlan) => boolean;
  handleFreePlanSubscription: (_plan: PricingPlan) => Promise<boolean>;
  determineSubscriptionFlow: () => {
    hasRazorpaySubscription: boolean;
    isCardPayment: boolean;
    isUpiOrEmandateOrUnknown: boolean;
    paymentMethod: string;
  };

  // Additional required functions
  setActiveTab: (_tab: "plans" | "subscription") => void;
}

export function useSubscriptionHandler(props: UseSubscriptionHandlerProps) {
  const router = useRouter();

  const {
    currentSubscriptionId,
    subscriptionStatus,
    currentPlanDetails: _currentPlanDetails,
    currentPlanCycle: _currentPlanCycle,
    lastPaymentMethod,
    razorpaySubscriptionId,
    dialogPlan,
    billingCycle,
    setDialogLoading,
    setIsPlanDialogOpen,
    setIsUpiWarningDialogOpen,
    setPendingSubscribeAction,
    startProcessing,
    completeProcessing,
    resetProcessing,
    setSubscriptionCreated,
    setFuturePaymentAuthorized,
    validateSubscriptionRequest,
    handleFreePlanSubscription,
    determineSubscriptionFlow,
    setActiveTab,
  } = props;





  // Main subscription handler - now using centralized logic
  const handleDialogSubscribe = async () => {
    console.log(`[CENTRALIZED_SUBSCRIPTION] handleDialogSubscribe called`);
    console.log(`[CENTRALIZED_SUBSCRIPTION] Current state:`, {
      dialogPlan: dialogPlan?.id,
      currentSubscriptionId,
      subscriptionStatus,
      lastPaymentMethod,
      razorpaySubscriptionId,
    });

    if (!dialogPlan) return;

    // Set dialog loading state to true
    setDialogLoading(true);

    try {
      // Check if this is a free plan - handle separately for now
      if (dialogPlan.id === "free") {
        const success = await handleFreePlanSubscription(dialogPlan);
        if (success) return;
      }

      // Validate subscription request
      if (!validateSubscriptionRequest(dialogPlan)) {
        setDialogLoading(false);
        return;
      }

      // Create a subscription action that returns a Response object for DialogManager
      const subscriptionAction = async () => {
        const response = await fetch('/api/subscriptions/centralized', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            planId: dialogPlan.id,
            planCycle: billingCycle,
          }),
        });
        return response;
      };

      // Determine if we need to show UPI warning or execute immediately
      const { isUpiOrEmandateOrUnknown } = determineSubscriptionFlow();
      const hasExistingSubscription = !!currentSubscriptionId && (subscriptionStatus === "authenticated" || subscriptionStatus === "active");

      // For trial users or users without existing subscriptions, execute immediately
      // For existing subscribers with UPI/eMandate, show warning dialog
      if (!hasExistingSubscription || !isUpiOrEmandateOrUnknown) {
        console.log('[SUBSCRIPTION_HANDLER] Executing subscription action immediately');

        // Execute the action immediately
        try {
          startProcessing("Processing your subscription request...");

          const actionResult = await subscriptionAction();
          const data = await actionResult.json();

          if (!data.success) {
            const errorMessage = data.error || "Failed to create subscription. Please try again.";
            toast.error("Subscription Error", { description: errorMessage });
            completeProcessing(false, errorMessage);
            return;
          }

          // Get subscription ID from response
          const subscriptionId = data.data?.id || data.data?.subscription_id;

          if (subscriptionId) {
            console.log("[SUBSCRIPTION_HANDLER] Got subscription ID:", subscriptionId);

            // Update processing message
            startProcessing("Opening payment authorization...");

            // Fetch Razorpay key ID
            const keyResponse = await fetch("/api/razorpay/key");
            if (!keyResponse.ok) {
              throw new Error("Failed to fetch Razorpay key ID");
            }

            const keyData = await keyResponse.json();
            if (!keyData.success || !keyData.key_id) {
              throw new Error("Invalid Razorpay key ID response");
            }

            // Import the Razorpay function
            const { openRazorpaySubscriptionCheckout } = await import("@/lib/razorpay/utils/loadRazorpaySDK");

            // Open Razorpay checkout
            const response = await openRazorpaySubscriptionCheckout(
              keyData.key_id,
              subscriptionId,
              {
                name: "Dukancard",
                description: `${dialogPlan.name} Plan (${billingCycle})`,
                theme: { color: "#6366f1" },
                prefill: { name: "", email: "", contact: "" },
                notes: {}
              }
            );

            // Handle payment success
            if (response.razorpay_payment_id) {
              startProcessing("Confirming payment...");

              // Import and call payment confirmation
              const { confirmSubscriptionPayment } = await import("@/lib/actions/subscription/confirm");
              const confirmResult = await confirmSubscriptionPayment(
                subscriptionId,
                response.razorpay_payment_id
              );

              if (!confirmResult.success) {
                throw new Error(confirmResult.error || "Failed to confirm payment");
              }

              // Show success message
              const isFuturePayment = confirmResult.data?.is_future_payment === true;
              if (isFuturePayment) {
                setFuturePaymentAuthorized("Your subscription has been authorized. Payment will be processed when your trial ends.");
              } else {
                toast.success("Payment Authorized", {
                  description: `Your ${dialogPlan.name} plan subscription has been authorized successfully.`,
                });
              }

              setSubscriptionCreated(`Your ${dialogPlan.name} plan subscription has been created and authorized successfully.`);
              setActiveTab("subscription");
              setIsPlanDialogOpen(false);

              // Refresh the page
              setTimeout(() => {
                router.refresh();
              }, 1500);
            }
          } else {
            throw new Error("No subscription ID returned from server");
          }
        } catch (paymentError) {
          console.error("Payment error:", paymentError);

          if (paymentError && typeof paymentError === 'object' && 'cancelled' in paymentError) {
            console.log("User cancelled the payment process");
            toast.dismiss();
            resetProcessing();
          } else {
            toast.error("Payment Authorization Failed", {
              description: paymentError instanceof Error ? paymentError.message : "Failed to authorize payment. Please try again.",
            });
            completeProcessing(false, "Payment authorization failed. Please try again.");
          }
        }
      } else {
        console.log('[SUBSCRIPTION_HANDLER] Showing UPI warning dialog for existing subscription'); 

        // Set the pending action for DialogManager to handle via UPI warning
        setPendingSubscribeAction(subscriptionAction);

        // Show UPI warning dialog
        setIsUpiWarningDialogOpen(true);
        setIsPlanDialogOpen(false);
      }

    } catch (error) {
      console.error("Error in subscription handler:", error);

      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred. Please try again.";

      // Show error toast
      toast.error("Subscription Error", {
        description: errorMessage,
      });

      // Reset processing state
      completeProcessing(false, errorMessage);
    } finally {
      // Reset dialog loading state
      setDialogLoading(false);
    }
  };

  return {
    handleDialogSubscribe,
  };
}
