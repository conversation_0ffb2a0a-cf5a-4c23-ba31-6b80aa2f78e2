// __mocks__/csrf-protection.js

// Mock the methods of the CsrfProtection class
const mockCreate = jest.fn();
const mockVerify = jest.fn();

// Mock the class itself
class MockCsrfProtection {
  constructor() { // Removed unused parameter
    // Constructor logic if needed
  }

  create = mockCreate;
  verify = mockVerify;
}

// Export the mock class as the default export
module.exports = MockCsrfProtection;

// Helper function to reset mocks
module.exports.__resetMocks = () => {
  mockCreate.mockClear();
  mockVerify.mockClear();

  // Set default resolved values (can be overridden in tests)
  // Assume create resolves with a mock token
  mockCreate.mockResolvedValue('mock-csrf-token');
  // Assume verify resolves successfully (no error)
  mockVerify.mockResolvedValue(undefined);
};
