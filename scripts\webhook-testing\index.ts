/**
 * W<PERSON><PERSON><PERSON><PERSON>OK TESTING SUITE INDEX
 * 
 * Central entry point for all webhook testing functionality
 */

// Export all scenarios
export * from './scenarios/subscriptionScenarios';

// Export all events
export * from './events/webhookEvents';

// Export test runners
export * from './runners/scenarioTestRunner';

// Export main test functions
export { runComprehensiveTests, generateReport } from './comprehensive-webhook-test';

/**
 * Available test commands:
 * 
 * 1. Comprehensive Test (all scenarios):
 *    npx tsx scripts/webhook-testing/comprehensive-webhook-test.ts
 * 
 * 2. Trial Scenarios Only:
 *    npx tsx scripts/webhook-testing/test-trial-scenarios.ts
 * 
 * 3. Paid Scenarios Only:
 *    npx tsx scripts/webhook-testing/test-paid-scenarios.ts
 * 
 * 4. Transition Scenarios Only:
 *    npx tsx scripts/webhook-testing/test-transition-scenarios.ts
 * 
 * 5. Webhook Event Coverage:
 *    npx tsx scripts/webhook-testing/test-webhook-events.ts
 */

export const TEST_COMMANDS = {
  COMPREHENSIVE: 'npx tsx scripts/webhook-testing/comprehensive-webhook-test.ts',
  TRIAL: 'npx tsx scripts/webhook-testing/test-trial-scenarios.ts',
  PAID: 'npx tsx scripts/webhook-testing/test-paid-scenarios.ts',
  TRANSITION: 'npx tsx scripts/webhook-testing/test-transition-scenarios.ts',
  EVENTS: 'npx tsx scripts/webhook-testing/test-webhook-events.ts'
} as const;
