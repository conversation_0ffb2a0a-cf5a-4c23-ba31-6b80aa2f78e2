export type SubscriptionStatus = 
  | 'active'
  | 'trialing'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'paused'
  | 'unknown';

export type PlanType = 'free' | 'basic' | 'growth' | 'enterprise';
export type PlanCycle = 'monthly' | 'yearly';

export interface DatabaseState {
  subscriptionStatus: string;
  planId: string;
  hasActiveSubscription: boolean;
  lastWebhookTimestamp?: string;
}

export interface ProcessedWebhookEvent {
  event_id: string;
  event_type: string;
  entity_type: string;
  entity_id: string;
  status: 'pending' | 'processed' | 'failed';
  payload: Record<string, unknown>;
  created_at: string;
  notes?: string;
}
