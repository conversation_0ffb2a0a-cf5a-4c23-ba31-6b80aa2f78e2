import { SupabaseClient } from "@supabase/supabase-js";
import { createAdminClient } from "@/utils/supabase/admin";

export class EventManager {
  private adminClient: SupabaseClient;

  constructor() {
    this.adminClient = createAdminClient();
  }

  async checkEventIdempotency(eventId: string): Promise<boolean> {
    const { data, error } = await this.adminClient
      .from("processed_webhook_events")
      .select("event_id")
      .eq("event_id", eventId)
      .eq("status", "success")
      .maybeSingle();

    if (error) {
      console.error(`[EventManager] Error checking event idempotency:`, error);
      return false; // Allow processing if we can't check
    }

    return !!data;
  }

  async markEventAsProcessing(eventId: string, eventType: string): Promise<void> {
    const { error } = await this.adminClient
      .from("processed_webhook_events")
      .upsert({
        event_id: eventId,
        event_type: eventType,
        status: "processing",
        processed_at: new Date().toISOString()
      });

    if (error) {
      console.error(`[EventManager] Error marking event as processing:`, error);
      // Don't throw - this is not critical for processing
    }
  }

  async markEventAsSuccess(eventId: string, message: string = "Successfully processed"): Promise<void> {
    const { error } = await this.adminClient
      .from("processed_webhook_events")
      .update({
        status: 'processed',
        processed_at: new Date().toISOString(),
        error_message: null,
        notes: message
      })
      .eq("event_id", eventId);

    if (error) {
      console.error(`[EventManager] Error marking event as success:`, error);
    }
  }

  async markEventAsFailed(eventId: string, errorMessage: string): Promise<void> {
    const { error } = await this.adminClient
      .from("processed_webhook_events")
      .update({
        status: "failed",
        processed_at: new Date().toISOString(),
        error_message: errorMessage
      })
      .eq("event_id", eventId);

    if (error) {
      console.error(`[EventManager] Error marking event as failed:`, error);
    }
  }
}

export const eventManager = new EventManager();