import { SUBSCRIPTION_STATUS } from "./subscription-constants";
import { SubscriptionStateManager } from "./subscription-state-manager";
import { createAdminClient } from "@/utils/supabase/admin";

/**
 * CENTRALIZED SUBSCRIPTION STATE VALIDATION
 *
 * Validates the entire subscription state for consistency.
 * This function should be used whenever we need to verify subscription integrity.
 */
export interface SubscriptionStateValidation {
  isValid: boolean;
  hasActiveSubscription: boolean;
  accessLevel: 'free' | 'trial' | 'paid';
  warnings: string[];
  errors: string[];
}

/**
 * Validates subscription state for consistency across business_profiles and payment_subscriptions
 * Uses the centralized SubscriptionStateManager for all logic
 */
export function validateSubscriptionState(
  businessProfile: {
    has_active_subscription: boolean;
    trial_end_date: string | null;
  },
  paymentSubscription: {
    subscription_status: string;
    plan_id: string;
  } | null
): SubscriptionStateValidation {
  const warnings: string[] = [];
  const errors: string[] = [];

  // Determine expected state based on payment subscription using centralized manager
  let expectedHasActiveSubscription = false;
  let accessLevel: 'free' | 'trial' | 'paid' = 'free';

  if (paymentSubscription) {
    expectedHasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(
      paymentSubscription.subscription_status,
      paymentSubscription.plan_id
    );

    accessLevel = SubscriptionStateManager.getAccessLevel(
      paymentSubscription.subscription_status,
      paymentSubscription.plan_id
    );
  }

  // Check for inconsistencies
  if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {
    errors.push(
      `has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`
    );
  }

  // Check trial state consistency
  if (paymentSubscription?.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
    if (!businessProfile.trial_end_date) {
      warnings.push('Trial status but no trial_end_date set');
    } else {
      const trialEnd = new Date(businessProfile.trial_end_date);
      const now = new Date();
      if (trialEnd <= now) {
        warnings.push('Trial status but trial period has expired');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    hasActiveSubscription: expectedHasActiveSubscription,
    accessLevel,
    warnings,
    errors
  };
}

/**
 * ENHANCED SUBSCRIPTION STATE VALIDATOR
 *
 * Validates and fixes subscription state inconsistencies across tables
 */
export async function validateAndFixSubscriptionState(
  businessProfileId: string
): Promise<{ success: boolean; message: string; fixed: boolean }> {
  try {
    const adminClient = createAdminClient();

    // Get current state from both tables
    const [profileResult, subscriptionResult] = await Promise.all([
      adminClient
        .from('business_profiles')
        .select('has_active_subscription, trial_end_date')
        .eq('id', businessProfileId)
        .maybeSingle(),
      adminClient
        .from('payment_subscriptions')
        .select('subscription_status, plan_id')
        .eq('business_profile_id', businessProfileId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle()
    ]);

    if (profileResult.error) {
      return { success: false, message: `Profile fetch error: ${profileResult.error.message}`, fixed: false };
    }

    if (!profileResult.data) {
      return { success: false, message: 'Business profile not found', fixed: false };
    }

    // Use centralized validation
    const validation = validateSubscriptionState(
      profileResult.data,
      subscriptionResult.data
    );

    if (validation.isValid) {
      return { success: true, message: 'Subscription state is consistent', fixed: false };
    }

    // Fix inconsistencies
    const expectedHasActiveSubscription = validation.hasActiveSubscription;

    if (profileResult.data.has_active_subscription !== expectedHasActiveSubscription) {
      const { error: fixError } = await adminClient
        .from('business_profiles')
        .update({
          has_active_subscription: expectedHasActiveSubscription,
          updated_at: new Date().toISOString()
        })
        .eq('id', businessProfileId);

      if (fixError) {
        return {
          success: false,
          message: `Failed to fix inconsistency: ${fixError.message}`,
          fixed: false
        };
      }

      console.log(`[STATE_VALIDATOR] Fixed has_active_subscription for ${businessProfileId}: ${profileResult.data.has_active_subscription} -> ${expectedHasActiveSubscription}`);
      return {
        success: true,
        message: `Fixed subscription state inconsistency`,
        fixed: true
      };
    }

    return { success: true, message: 'No fixes needed', fixed: false };
  } catch (error) {
    console.error(`[STATE_VALIDATOR] Exception:`, error);
    return {
      success: false,
      message: `Validation exception: ${error instanceof Error ? error.message : String(error)}`,
      fixed: false
    };
  }
}