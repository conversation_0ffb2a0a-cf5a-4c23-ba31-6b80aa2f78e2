// Subscription status constants - IMMUT<PERSON>LE SINGLE SOURCE OF TRUTH
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  AUTHENTICATED: 'authenticated',
  TRIAL: 'trial',
  PENDING: 'pending',
  HALTED: 'halted',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  COMPLETED: 'completed',
  PAYMENT_FAILED: 'payment_failed',
  CANCELLATION_SCHEDULED: 'cancellation_scheduled'
} as const;

export type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];

// Plan ID constants for consistency
export const PLAN_IDS = {
  FREE: 'free',
  BASIC: 'basic',
  GROWTH: 'growth',
  PRO: 'pro',
  ENTERPRISE: 'enterprise'
} as const;

export type PlanId = typeof PLAN_IDS[keyof typeof PLAN_IDS];