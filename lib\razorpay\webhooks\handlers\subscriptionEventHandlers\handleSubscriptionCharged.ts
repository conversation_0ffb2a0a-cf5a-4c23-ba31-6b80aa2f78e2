import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { createAdminClient } from "@/utils/supabase/admin";
import {
  isTerminalStatus,
  SUBSCRIPTION_STATUS,
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";

/**
 * Handle subscription.charged event
 *
 * This event is triggered when a subscription payment is successfully charged.
 * It updates all subscription date fields, including:
 * - subscription_start_date: The start date of the current billing cycle
 * - subscription_expiry_time: The end date of the current billing cycle
 * - subscription_charge_time: The date when the next payment will be charged
 *
 * This is especially important for recurring subscriptions, as it ensures
 * that the subscription_charge_time is always updated to reflect the next payment date
 * whenever a subscription is renewed.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionCharged(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract payment data from payload (primary for charged events)
    const payment = payload.payload.payment;
    const subscriptionData = payload.payload.subscription;

    if (!payment || !payment.entity) {
      console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload");
      return { success: false, message: "Payment data not found in payload" };
    }

    // For charged events, subscription ID comes from payment.subscription_id
    const paymentEntity = payment.entity as { subscription_id?: string };
    const subscriptionId = paymentEntity.subscription_id;
    if (!subscriptionId) {
      console.error("[RAZORPAY_WEBHOOK] Subscription ID not found in payment data");
      return { success: false, message: "Subscription ID not found in payment data" };
    }

    // Subscription data is optional for charged events
    let subscription: RazorpaySubscription | null = null;
    if (subscriptionData && subscriptionData.entity) {
      subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    }
    console.log(`[RAZORPAY_WEBHOOK] Subscription charged: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.charged',
      eventId: razorpayEventId || `charged_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Check if this subscription is in a terminal state (cancelled, expired, completed)
    const { data: existingSubscription, error: checkError } = await adminClient
      .from("payment_subscriptions")
      .select("cancelled_at, plan_id, subscription_status")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (checkError) {
      console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${subscriptionId}:`, checkError);
      // Continue processing anyway
    } else if (existingSubscription) {
      // CENTRALIZED LOGIC: Check if subscription is in a terminal state
      // CRITICAL FIX: Don't check cancelled_at alone - only check actual subscription_status
      // Trial users can have cancelled_at but should still be able to process charges
      // Free plan users should not be able to process paid charges
      const isTerminalState = existingSubscription.plan_id === "free" ||
                             isTerminalStatus(existingSubscription.subscription_status);

      if (isTerminalState) {
        console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is in terminal state (plan_id: ${existingSubscription.plan_id}, status: ${existingSubscription.subscription_status}), skipping charge processing`);
        return { success: true, message: "Subscription is in terminal state, skipping charge processing" };
      }

      // ADDITIONAL CHECK: Allow trial users to process charges even if they have cancelled_at
      // This is critical for trial-to-paid transitions
      if (existingSubscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
        console.log(`[RAZORPAY_WEBHOOK] Trial user ${subscriptionId} processing charge - this is allowed even with cancelled_at timestamp`);
      }
    }

    // CRITICAL FIX: Use centralized webhookProcessor for all subscription updates
    // This ensures proper sequence validation and consistent state management
    const planTypeFromNotesCharged = subscription?.notes?.plan_type || payment.entity.notes?.plan_type;
    const planCycleFromNotesCharged = subscription?.notes?.plan_cycle || payment.entity.notes?.plan_cycle;

    const updateResult = await webhookProcessor.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.ACTIVE,
      {
        // Additional data to update
        last_payment_id: payment.entity.id,
        last_payment_date: new Date(payment.entity.created_at * 1000).toISOString(),
        last_payment_method: payment.entity.method,
        subscription_start_date: subscription?.current_start ? new Date(subscription.current_start * 1000).toISOString() : null,
        subscription_expiry_time: subscription?.current_end ? new Date(subscription.current_end * 1000).toISOString() : null,
        subscription_charge_time: subscription?.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
        razorpay_customer_id: subscription?.customer_id || null,
        // CRITICAL: Clear cancellation_requested_at when subscription is charged
        // This allows trial users to transition to paid even if they previously cancelled
        cancellation_requested_at: null,
        // Ensure plan details are passed for the RPC
        plan_id: planTypeFromNotesCharged,
        plan_cycle: planCycleFromNotesCharged,
        razorpay_plan_id: subscription?.plan_id
      },
      webhookTimestamp // Pass webhook timestamp for sequence validation
    );

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, updateResult.message);
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
    }

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription charged:", error);

    // Mark event as failed
    const errorMessage = `Error handling subscription charged: ${error instanceof Error ? error.message : String(error)}`;
    if (context) {
      await webhookProcessor.markEventAsFailed(context.eventId, errorMessage);
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}