#!/usr/bin/env tsx

/**
 * PRODUCTION STRESS TEST SUITE
 * 
 * Tests real-world production scenarios that could cause bad user experience
 * Focuses on edge cases, concurrent operations, and system reliability
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { ScenarioTestRunner } from './runners/scenarioTestRunner';
import { PRODUCTION_EDGE_CASES, STRESS_TEST_SCENARIOS } from './scenarios/subscriptionScenarios';

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

interface StressTestResult {
  scenario: string;
  success: boolean;
  duration: number;
  error?: string;
  category: string;
}

async function runProductionStressTests() {
  console.log('🚀 Production Stress Test Suite');
  console.log('================================');
  console.log(`📋 Business ID: ${BUSINESS_ID}`);
  
  const allScenarios = [...PRODUCTION_EDGE_CASES, ...STRESS_TEST_SCENARIOS];
  console.log(`📊 Total Stress Test Scenarios: ${allScenarios.length}`);
  console.log('');

  const runner = new ScenarioTestRunner(BUSINESS_ID);
  await runner.initialize(); // Initialize the Supabase client
  const results: StressTestResult[] = [];
  
  // Group scenarios by category for better organization
  const categories = {
    'Network & Timing': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('delayed') || s.id.includes('rapid') || s.id.includes('successive')
    ),
    'Payment Failures': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('payment_failure') || s.id.includes('registration_failure')
    ),
    'User Behavior': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('user_') || s.id.includes('multiple_times')
    ),
    'Lifecycle Edge Cases': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('expires') || s.id.includes('completed') || s.id.includes('review')
    ),
    'Data Consistency': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('mismatch') || s.id.includes('corruption')
    ),
    'Enterprise Scenarios': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('enterprise')
    ),
    'Concurrent Operations': PRODUCTION_EDGE_CASES.filter(s => 
      s.id.includes('concurrent')
    ),
    'Stress Tests': STRESS_TEST_SCENARIOS
  };

  let totalPassed = 0;
  let totalFailed = 0;

  for (const [categoryName, scenarios] of Object.entries(categories)) {
    if (scenarios.length === 0) continue;
    
    console.log(`\n🔍 ${categoryName} (${scenarios.length} tests)`);
    console.log('─'.repeat(50));
    
    let categoryPassed = 0;
    let categoryFailed = 0;

    for (const scenario of scenarios) {
      const startTime = Date.now();
      
      try {
        console.log(`🧪 Testing: ${scenario.name}`);
        console.log(`   Description: ${scenario.description}`);
        console.log(`   Initial: ${scenario.initialState.subscription_status}/${scenario.initialState.plan_id}/${scenario.initialState.has_active_subscription}`);
        
        const result = await runner.runScenario(scenario);
        const duration = (Date.now() - startTime) / 1000;
        
        if (result.success === scenario.shouldSucceed) {
          console.log(`   ✅ PASS ${scenario.name} (${duration.toFixed(2)}s)`);
          categoryPassed++;
          totalPassed++;
          
          results.push({
            scenario: scenario.name,
            success: true,
            duration,
            category: categoryName
          });
        } else {
          const expectedResult = scenario.shouldSucceed ? 'succeed' : 'fail';
          const actualResult = result.success ? 'succeeded' : 'failed';
          console.log(`   ❌ FAIL ${scenario.name} (${duration.toFixed(2)}s)`);
          console.log(`   Expected to ${expectedResult}, but ${actualResult}: ${result.message}`);
          categoryFailed++;
          totalFailed++;
          
          results.push({
            scenario: scenario.name,
            success: false,
            duration,
            error: `Expected to ${expectedResult}, but ${actualResult}: ${result.message}`,
            category: categoryName
          });
        }
        
      } catch (error) {
        const duration = (Date.now() - startTime) / 1000;
        console.log(`   💥 ERROR ${scenario.name} (${duration.toFixed(2)}s)`);
        console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
        categoryFailed++;
        totalFailed++;
        
        results.push({
          scenario: scenario.name,
          success: false,
          duration,
          error: error instanceof Error ? error.message : String(error),
          category: categoryName
        });
      }
      
      // Small delay between tests to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const categorySuccessRate = ((categoryPassed / (categoryPassed + categoryFailed)) * 100).toFixed(1);
    console.log(`\n📊 ${categoryName} Results: ${categoryPassed}/${categoryPassed + categoryFailed} passed (${categorySuccessRate}%)`);
  }

  // Generate comprehensive report
  generateStressTestReport(results, totalPassed, totalFailed);
  
  const overallSuccessRate = ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1);
  console.log(`\n🎯 Overall Stress Test Results:`);
  console.log(`   Total Tests: ${totalPassed + totalFailed}`);
  console.log(`   Passed: ${totalPassed}`);
  console.log(`   Failed: ${totalFailed}`);
  console.log(`   Success Rate: ${overallSuccessRate}%`);
  
  // Determine if system is production ready
  const isProductionReady = parseFloat(overallSuccessRate) >= 95.0;
  
  if (isProductionReady) {
    console.log('\n🎉 PRODUCTION READY!');
    console.log('✅ System handles all critical production scenarios');
    console.log('✅ Edge cases and stress conditions managed properly');
    console.log('✅ User experience protected under adverse conditions');
  } else {
    console.log('\n⚠️ PRODUCTION READINESS CONCERNS');
    console.log('🔧 Some critical scenarios need attention before production');
    console.log('📋 Review failed tests and implement fixes');
  }
  
  process.exit(isProductionReady ? 0 : 1);
}

function generateStressTestReport(results: StressTestResult[], _totalPassed: number, _totalFailed: number) {
  console.log('\n' + '='.repeat(60));
  console.log('📈 PRODUCTION STRESS TEST REPORT');
  console.log('='.repeat(60));
  
  // Group results by category
  const categoryResults = results.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = { passed: 0, failed: 0, scenarios: [] };
    }
    if (result.success) {
      acc[result.category].passed++;
    } else {
      acc[result.category].failed++;
    }
    acc[result.category].scenarios.push(result);
    return acc;
  }, {} as Record<string, { passed: number; failed: number; scenarios: StressTestResult[] }>);
  
  // Display category breakdown
  for (const [category, data] of Object.entries(categoryResults)) {
    const total = data.passed + data.failed;
    const successRate = ((data.passed / total) * 100).toFixed(1);
    console.log(`\n📊 ${category}:`);
    console.log(`   Tests: ${total} | Passed: ${data.passed} | Failed: ${data.failed} | Success: ${successRate}%`);
    
    if (data.failed > 0) {
      console.log(`   Failed scenarios:`);
      data.scenarios.filter(s => !s.success).forEach(scenario => {
        console.log(`   • ${scenario.scenario}: ${scenario.error}`);
      });
    }
  }
  
  // Performance metrics
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  const maxDuration = Math.max(...results.map(r => r.duration));
  const minDuration = Math.min(...results.map(r => r.duration));
  
  console.log(`\n⏱️ Performance Metrics:`);
  console.log(`   Average Duration: ${avgDuration.toFixed(2)}s`);
  console.log(`   Max Duration: ${maxDuration.toFixed(2)}s`);
  console.log(`   Min Duration: ${minDuration.toFixed(2)}s`);
  
  // Critical findings
  const criticalFailures = results.filter(r => 
    !r.success && (
      r.scenario.includes('Payment Failure') ||
      r.scenario.includes('Data Consistency') ||
      r.scenario.includes('Concurrent')
    )
  );
  
  if (criticalFailures.length > 0) {
    console.log(`\n🚨 Critical Failures (${criticalFailures.length}):`);
    criticalFailures.forEach(failure => {
      console.log(`   • ${failure.scenario}: ${failure.error}`);
    });
  }
  
  console.log('='.repeat(60));
}

runProductionStressTests().catch(console.error);
